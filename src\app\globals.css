@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-nunito);
  --font-mono: var(--font-inter);
  --color-cream: #f5f5dc;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-nunito), 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Mobile-friendly typography */
@media (max-width: 768px) {
  body {
    font-size: 18px;
    line-height: 1.7;
  }

  h1 {
    font-size: 1.75rem;
    line-height: 1.3;
  }

  h2 {
    font-size: 1.5rem;
    line-height: 1.4;
  }

  h3 {
    font-size: 1.25rem;
    line-height: 1.4;
  }

  button, .btn {
    min-height: 44px;
    font-size: 1rem;
    font-weight: 600;
  }

  a, .link {
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
  }
}

/* Vietnamese text optimization */
.vietnamese-text {
  font-family: var(--font-nunito), 'Times New Roman', serif;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.bg-cream {
  background-color: #f5f5dc;
}

/* Improved button styles for mobile */
.mobile-btn {
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  min-height: 48px;
  touch-action: manipulation;
  user-select: none;
  transition: all 0.2s ease-in-out;
}

.mobile-btn:active {
  transform: scale(0.98);
}

/* Better spacing for mobile cards */
.mobile-card {
  padding: 16px;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Improved text contrast for readability */
.text-readable {
  color: #1f2937;
  font-weight: 500;
}

.text-readable-light {
  color: #374151;
  font-weight: 400;
}
